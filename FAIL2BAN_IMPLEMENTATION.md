# Fail2ban Implementation for Flash Preview Server

## Overview

This document describes the fail2ban implementation added to the Flash Preview Server for enhanced security and intrusion prevention.

## What was implemented

### 1. System Components Added

- **fail2ban**: Intrusion prevention system
- **iptables**: Network packet filtering
- **rsyslog**: System logging service

### 2. Configuration Files Created

#### Fail2ban Configuration
- `ci/image-config-files/fail2ban/jail.local` - Main jail configuration
- `ci/image-config-files/fail2ban/filter.d/flash-preview-api.conf` - API abuse detection
- `ci/image-config-files/fail2ban/filter.d/flash-preview-auth.conf` - Authentication failure detection
- `ci/image-config-files/fail2ban/filter.d/nginx-badbots.conf` - Bot and scanner detection
- `ci/image-config-files/fail2ban/action.d/iptables-multiport-log.conf` - Custom action with logging

#### Monitoring Scripts
- `ci/image-config-files/common/fail2ban-status.sh` - Comprehensive status monitoring
- `ci/image-config-files/common/test-fail2ban.sh` - Configuration testing script

### 3. Modified Files

#### Docker Configuration
- `ci/Dockerfile` - Added fail2ban, iptables, rsyslog packages and configuration
- `ci/docker-entrypoint.sh` - Added fail2ban setup and initialization
- `ci/image-config-files/supervisor/supervisord.conf` - Added fail2ban and rsyslog processes

#### Nginx Configuration
- `ci/image-config-files/nginx/nginx.conf` - Enhanced logging for fail2ban monitoring

#### Application Code
- `src/routes/status.ts` - Added fail2ban status monitoring and API endpoint

## Security Features

### Active Jails

1. **sshd** - SSH brute force protection
   - Max retries: 3
   - Ban time: 1 hour
   - Monitors: `/var/log/auth.log`

2. **nginx-http-auth** - Basic auth failure protection
   - Max retries: 3
   - Ban time: 30 minutes
   - Monitors: `/var/log/nginx/access.log`

3. **nginx-noscript** - Script injection detection
   - Max retries: 6
   - Ban time: 10 minutes
   - Monitors: `/var/log/nginx/access.log`

4. **nginx-badbots** - Malicious bot detection
   - Max retries: 2
   - Ban time: 1 hour
   - Monitors: `/var/log/nginx/access.log`

5. **nginx-noproxy** - Proxy abuse prevention
   - Max retries: 2
   - Ban time: 1 hour
   - Monitors: `/var/log/nginx/access.log`

6. **nginx-limit-req** - Rate limiting
   - Max retries: 10
   - Ban time: 10 minutes
   - Monitors: `/var/log/nginx/error.log`

7. **flash-preview-api** - API abuse protection
   - Max retries: 10 in 5 minutes
   - Ban time: 30 minutes
   - Monitors: `/var/log/nginx/access.log`

8. **flash-preview-auth** - Authentication failure protection
   - Max retries: 5 in 10 minutes
   - Ban time: 1 hour
   - Monitors: `/var/log/nginx/access.log`

### Monitoring and Logging

- **Main log**: `/var/log/fail2ban.log`
- **Action log**: `/var/log/fail2ban-actions.log`
- **API endpoint**: `GET /fail2ban` - Real-time status and statistics
- **Status script**: `/root/common/fail2ban-status.sh` - Comprehensive status report
- **Test script**: `/root/common/test-fail2ban.sh` - Configuration validation

## API Integration

### Status Endpoint Enhancement
The main status endpoint (`GET /`) now includes fail2ban status:

```json
{
  "components": {
    "fail2ban": {
      "status": "ok|error",
      "details": {
        "jails": {
          "jail-name": {
            "currentlyBanned": 0,
            "totalBanned": 5
          }
        },
        "activeJails": 8
      }
    }
  }
}
```

### Dedicated Fail2ban Endpoint
New endpoint `GET /fail2ban` provides detailed security information:

```json
{
  "status": "ok",
  "details": {
    "jails": { /* jail details */ },
    "activeJails": 8
  },
  "logs": {
    "recent": ["log entries..."],
    "actions": ["ban/unban actions..."]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Testing and Validation

### Pre-deployment Testing
Run the configuration test script:
```bash
/root/common/test-fail2ban.sh
```

### Runtime Monitoring
Check fail2ban status:
```bash
/root/common/fail2ban-status.sh
```

### API Monitoring
```bash
# Check overall system status
curl http://api.${DOMAIN}/

# Check detailed fail2ban status
curl http://api.${DOMAIN}/fail2ban
```

## Deployment Notes

### Container Requirements
- Requires privileged container or specific capabilities for iptables
- Needs access to system logs
- Requires persistent storage for ban state (optional but recommended)

### Environment Variables
No additional environment variables required. Fail2ban uses existing nginx and system logs.

### Performance Impact
- Minimal CPU overhead
- Small memory footprint (~10-20MB)
- Log processing is efficient and non-blocking

## Troubleshooting

### Common Issues
1. **iptables not working**: Normal in some container environments
2. **Permission denied**: Ensure container has necessary privileges
3. **Logs not found**: Check nginx and rsyslog are running

### Debug Commands
```bash
# Check fail2ban status
fail2ban-client status

# Check specific jail
fail2ban-client status jail-name

# Test configuration
fail2ban-client -t

# View recent bans
tail -f /var/log/fail2ban-actions.log
```

## Security Benefits

1. **Automated threat response** - Immediate IP blocking on suspicious activity
2. **Reduced attack surface** - Proactive blocking of malicious traffic
3. **Comprehensive monitoring** - Real-time security status and logging
4. **Customizable protection** - Tailored rules for the Flash Preview Server
5. **API integration** - Security status available programmatically
6. **Minimal maintenance** - Self-managing with automatic unban timers

## Future Enhancements

Potential improvements for future versions:
- Email notifications for security events
- Integration with external threat intelligence
- Whitelist management API
- Geographic IP blocking
- Advanced rate limiting rules
- Security dashboard UI
