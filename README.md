# Flash Preview Server

A server for managing Flash site previews with job queues and status tracking.

## Architecture Overview

### Docker Container

The application runs in a Docker container based on the `flash-2024-builder` image with the following key components:

- Node.js application (TypeScript)
- Nginx web server
- Redis server
- Supervisor process manager
- SQLite database
- Fail2ban intrusion prevention system
- rsyslog for system logging

### Service Architecture

#### Nginx Configuration
- Handles routing for both API and preview sites
- API endpoints: `api.${DOMAIN}`
- Preview sites: `{repo-name}.${DOMAIN}`
- Serves static files from `/root/app/sites/{repo}/build`
- Includes health check endpoint at `/health`
- WebSocket support enabled for real-time updates

#### Supervisor
Manages five main processes:
1. Nginx: Web server for static files and API proxy
2. Redis: Queue management and caching
3. Node.js: API server and job processor
4. rsyslog: System logging service
5. Fail2ban: Intrusion prevention system

#### Mounted Volumes
- `/root/app/sites`: Repository build outputs
- `/var/lib/redis`: Redis data persistence
- `/var/log`: Service logs

### URLs and Routing

#### API Endpoints
- Base URL: `http://api.${DOMAIN}`

##### Status Endpoint
- `GET /`
  - Returns system status including database, Redis, and queue health
  - Response includes component statuses and queue details
  - Access via browser to see system status dashboard

##### Queue Management UI
- `GET /queues`
  - BullMQ dashboard for monitoring job queues
  - Shows job statuses, progress, and history
  - Provides queue management capabilities

##### Job Management Endpoints
- `POST /deploy/:repo`
  - Triggers initial repository deployment
  - Creates job in sequential queue
  - Returns job details including ID
  - Parameters:
    - `:repo` - Repository name

- `POST /update/:repo`
  - Updates existing repository deployment
  - Creates job in sequential queue
  - Parameters:
    - `:repo` - Repository name

- `POST /update/:repo/story/:storyId`
  - Updates specific story in repository
  - Creates job in concurrent queue
  - Parameters:
    - `:repo` - Repository name
    - `:storyId` - Story identifier

- `GET /jobs`
  - Lists all jobs across all repositories
  - Returns job history and status

- `GET /repo/:repo/jobs`
  - Lists jobs for a specific repository
  - Parameters:
    - `:repo` - Repository name

- `GET /jobs/:jobId`
  - Gets a specific job by ID
  - Parameters:
    - `:jobId` - Job ID

##### Log Access Endpoints
- `GET /logs/api/out`
  - Access API server output logs
  - Returns plain text log content

- `GET /logs/api/error`
  - Access API server error logs
  - Returns plain text log content

- `GET /logs/nginx/out`
  - Access Nginx access logs
  - Returns plain text log content

- `GET /logs/nginx/error`
  - Access Nginx error logs
  - Returns plain text log content

- `GET /logs/queue/out`
  - Access queue worker output logs
  - Returns plain text log content

- `GET /logs/queue/error`
  - Access queue worker error logs
  - Returns plain text log content

##### Security Endpoints
- `GET /fail2ban`
  - Returns fail2ban status and security information
  - Shows active jails, banned IPs, and recent activity
  - Includes recent log entries and ban/unban actions

#### Queue Architecture
The system uses BullMQ with Redis for job processing:

##### Sequential Queue
- Name: `sequential`
- Used for operations that need to run one at a time
- Handles repository deployments and full updates
- Job types:
  - `DEPLOY`: Pulls new code from GH
  - `UPDATE`: Same code, rebuilds with new story content

##### Concurrent Queue
- Name: `concurrent`
- Used for operations that can run in parallel
- Handles individual story updates
- Job types:
  - `UPDATE_ONE`: Single story update


#### Preview Sites
- URL format: `http://{repo-name}.${DOMAIN}`
- Serves static content from repository builds
- Returns "loading" if build is in progress
- Returns 404 if repository doesn't exist

### Environment Variables

#### Required Variables
- `GH_APP_ID` - GitHub App ID
- `GH_APP_INSTALLATION_ID` - GitHub App Installation ID
- `GH_PRIVATE_KEY` - GitHub App Private Key
- `DOMAIN` - Server domain (defaults to 'preview.server')

#### Optional Variables
- `GH_ORG` - GitHub organization name (defaults to 'Together-Digital')
- `NODE_ENV` - Environment mode (development/production)
- `PORT` - API server port (defaults to 3000)
- `BASIC_AUTH_USER` - Username for basic authentication (optional)
- `BASIC_AUTH_PASS` - Password for basic authentication (optional)

### Security Features

#### Fail2ban Intrusion Prevention
The server includes fail2ban for automated intrusion prevention and security monitoring:

##### Active Jails
- **sshd**: Protects against SSH brute force attacks (3 attempts, 1 hour ban)
- **nginx-http-auth**: Monitors failed basic authentication attempts (3 attempts, 30 minute ban)
- **nginx-noscript**: Detects script injection attempts (6 attempts, 10 minute ban)
- **nginx-badbots**: Blocks malicious bots and scanners (2 attempts, 1 hour ban)
- **nginx-noproxy**: Prevents proxy abuse (2 attempts, 1 hour ban)
- **nginx-limit-req**: Rate limiting protection (10 attempts, 10 minute ban)
- **flash-preview-api**: API abuse protection (10 attempts in 5 minutes, 30 minute ban)
- **flash-preview-auth**: Authentication failure protection (5 attempts in 10 minutes, 1 hour ban)

##### Configuration
- Default ban time: 1 hour
- Find time window: 10 minutes
- Logs to: `/var/log/fail2ban.log`
- Action logs: `/var/log/fail2ban-actions.log`
- Uses iptables for IP blocking

##### Monitoring
- Status available via API endpoint: `GET /fail2ban`
- Manual status check: `/root/common/fail2ban-status.sh`
- Real-time monitoring through supervisor logs

#### Basic Authentication
- Optional HTTP Basic Authentication for all endpoints
- Configured via `BASIC_AUTH_USER` and `BASIC_AUTH_PASS` environment variables
- Applied at nginx level for performance
- Excludes POST requests to API endpoints for webhook compatibility

#### Network Security
- iptables-based IP blocking
- Rate limiting on API endpoints
- CORS headers configured for controlled access
- Nginx security headers enabled

### Available Scripts

#### Development
```bash
# Start development environment with Redis and server
npm run dev

# Start Redis separately
npm run dev:redis

# Start server with hot reload
npm run dev:server

# Watch TypeScript compilation
npm run build:watch
```

#### Production
```bash
# Build TypeScript
npm run build

# Start production server
npm run start

# Start Docker environment
npm run docker:up
```

#### Code Quality
```bash
# Run all checks
npm run check

# Type checking
npm run check:types

# Linting
npm run check:lint

# Format checking
npm run check:format

# Unused code check
npm run check:unused

# Fix formatting and linting
npm run fix
```

### Local Development Setup

#### Prerequisites
- Node.js 16.19.1
- Redis (for job queues)
- Docker and Docker Compose (for production environment)

#### Installation
1. Clone the repository
2. Install dependencies:
```bash
npm install
```
3. Copy `.env.example` to `.env` and configure environment variables
4. Start development environment:
```bash
npm run dev
```

### Docker Deployment

1. Build and start the container:
```bash
npm run docker:up
```

The container includes:
- Nginx for static file serving and API proxy
- Redis for job queues
- Supervisor for process management
- Node.js application server
- Fail2ban for intrusion prevention
- rsyslog for system logging
- iptables for network security

### Example Usage

```bash
# Deploy a repository
curl -X POST http://api.${DOMAIN}/deploy/my-repo

# Update a repository
curl -X POST http://api.${DOMAIN}/update/my-repo

# Update a specific story
curl -X POST http://api.${DOMAIN}/update/my-repo/story/123

# Get all jobs
curl http://api.${DOMAIN}/jobs

# Get jobs for a specific repository
curl http://api.${DOMAIN}/jobs/my-repo

# Check fail2ban security status
curl http://api.${DOMAIN}/fail2ban

# Check overall system status (includes fail2ban)
curl http://api.${DOMAIN}/
