# Flash Preview Server

A server for managing Flash site previews with job queues and status tracking.

## Architecture Overview

### Docker Container

The application runs in a Docker container based on the `flash-2024-builder` image with the following key components:

- Node.js application (TypeScript)
- Nginx web server
- Redis server
- Supervisor process manager
- SQLite database

### Service Architecture

#### Nginx Configuration
- Handles routing for both API and preview sites
- API endpoints: `api.${DOMAIN}`
- Preview sites: `{repo-name}.${DOMAIN}`
- Serves static files from `/root/app/sites/{repo}/build`
- Includes health check endpoint at `/health`
- WebSocket support enabled for real-time updates

#### Supervisor
Manages three main processes:
1. Nginx: Web server for static files and API proxy
2. Redis: Queue management and caching
3. Node.js: API server and job processor

#### Mounted Volumes
- `/root/app/sites`: Repository build outputs
- `/var/lib/redis`: Redis data persistence
- `/var/log`: Service logs

### URLs and Routing

#### API Endpoints
- Base URL: `http://api.${DOMAIN}`

##### Status Endpoint
- `GET /`
  - Returns system status including database, Redis, and queue health
  - Response includes component statuses and queue details
  - Access via browser to see system status dashboard

##### Queue Management UI
- `GET /queues`
  - BullMQ dashboard for monitoring job queues
  - Shows job statuses, progress, and history
  - Provides queue management capabilities

##### Job Management Endpoints
- `POST /deploy/:repo`
  - Triggers initial repository deployment
  - Creates job in sequential queue
  - Returns job details including ID
  - Parameters:
    - `:repo` - Repository name

- `POST /update/:repo`
  - Updates existing repository deployment
  - Creates job in sequential queue
  - Parameters:
    - `:repo` - Repository name

- `POST /update/:repo/story/:storyId`
  - Updates specific story in repository
  - Creates job in concurrent queue
  - Parameters:
    - `:repo` - Repository name
    - `:storyId` - Story identifier

- `GET /jobs`
  - Lists all jobs across all repositories
  - Returns job history and status

- `GET /repo/:repo/jobs`
  - Lists jobs for a specific repository
  - Parameters:
    - `:repo` - Repository name

- `GET /jobs/:jobId`
  - Gets a specific job by ID
  - Parameters:
    - `:jobId` - Job ID

##### Log Access Endpoints
- `GET /logs/api/out`
  - Access API server output logs
  - Returns plain text log content

- `GET /logs/api/error`
  - Access API server error logs
  - Returns plain text log content

- `GET /logs/nginx/out`
  - Access Nginx access logs
  - Returns plain text log content

- `GET /logs/nginx/error`
  - Access Nginx error logs
  - Returns plain text log content

- `GET /logs/queue/out`
  - Access queue worker output logs
  - Returns plain text log content

- `GET /logs/queue/error`
  - Access queue worker error logs
  - Returns plain text log content

#### Queue Architecture
The system uses BullMQ with Redis for job processing:

##### Sequential Queue
- Name: `sequential`
- Used for operations that need to run one at a time
- Handles repository deployments and full updates
- Job types: 
  - `DEPLOY`: Pulls new code from GH
  - `UPDATE`: Same code, rebuilds with new story content

##### Concurrent Queue
- Name: `concurrent`
- Used for operations that can run in parallel
- Handles individual story updates
- Job types:
  - `UPDATE_ONE`: Single story update


#### Preview Sites
- URL format: `http://{repo-name}.${DOMAIN}`
- Serves static content from repository builds
- Returns "loading" if build is in progress
- Returns 404 if repository doesn't exist

### Environment Variables

#### Required Variables
- `GH_APP_ID` - GitHub App ID
- `GH_APP_INSTALLATION_ID` - GitHub App Installation ID
- `GH_PRIVATE_KEY` - GitHub App Private Key
- `DOMAIN` - Server domain (defaults to 'preview.server')

#### Optional Variables
- `GH_ORG` - GitHub organization name (defaults to 'Together-Digital')
- `NODE_ENV` - Environment mode (development/production)
- `PORT` - API server port (defaults to 3000)

### Available Scripts

#### Development
```bash
# Start development environment with Redis and server
npm run dev

# Start Redis separately
npm run dev:redis

# Start server with hot reload
npm run dev:server

# Watch TypeScript compilation
npm run build:watch
```

#### Production
```bash
# Build TypeScript
npm run build

# Start production server
npm run start

# Start Docker environment
npm run docker:up
```

#### Code Quality
```bash
# Run all checks
npm run check

# Type checking
npm run check:types

# Linting
npm run check:lint

# Format checking
npm run check:format

# Unused code check
npm run check:unused

# Fix formatting and linting
npm run fix
```

### Local Development Setup

#### Prerequisites
- Node.js 16.19.1
- Redis (for job queues)
- Docker and Docker Compose (for production environment)

#### Installation
1. Clone the repository
2. Install dependencies:
```bash
npm install
```
3. Copy `.env.example` to `.env` and configure environment variables
4. Start development environment:
```bash
npm run dev
```

### Docker Deployment

1. Build and start the container:
```bash
npm run docker:up
```

The container includes:
- Nginx for static file serving and API proxy
- Redis for job queues
- Supervisor for process management
- Node.js application server

### Example Usage

```bash
# Deploy a repository
curl -X POST http://api.${DOMAIN}/deploy/my-repo

# Update a repository
curl -X POST http://api.${DOMAIN}/update/my-repo

# Update a specific story
curl -X POST http://api.${DOMAIN}/update/my-repo/story/123

# Get all jobs
curl http://api.${DOMAIN}/jobs

# Get jobs for a specific repository
curl http://api.${DOMAIN}/jobs/my-repo
