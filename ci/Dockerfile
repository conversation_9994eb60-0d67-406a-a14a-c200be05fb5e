FROM europe-west2-docker.pkg.dev/flash-2024-builder-8001/flash-2024-builder/flash-2024-builder:latest
LABEL name="flash-preview-server" version="1.0.0"

ARG LAST_COMMIT_MESSAGE
ARG BASIC_AUTH_USER
ARG BASIC_AUTH_PASS
ARG DOMAIN

ENV LAST_COMMIT_MESSAGE=${LAST_COMMIT_MESSAGE:-"Not available"}

# Install system dependencies
RUN apt-get update && \
    curl -fsSL https://packages.redis.io/gpg | gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/redis.list && \
    apt-get update && \
    apt-get install -y \
        nginx \
        sqlite3 \
        libsqlite3-dev \
        python3 \
        make \
        g++ \
        supervisor \
        curl \
        gpg \
        lsb-release \
        gettext-base \
        apache2-utils \
        redis \
        fail2ban \
        iptables \
        rsyslog && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /root/app

# Install and build Node.js application
COPY package*.json .npmrc tsconfig.json ./
COPY src/ ./src/

RUN npm ci && \
    npm run build

# Remove default nginx configs if they exist
RUN rm -f /etc/nginx/sites-enabled/default /etc/nginx/sites-available/default

# Copy configuration files
COPY ci/image-config-files/nginx/nginx.conf /etc/nginx/
COPY ci/image-config-files/nginx/conf.d/preview-sites.conf.template /etc/nginx/conf.d/
COPY ci/image-config-files/nginx/snippets/ /etc/nginx/snippets/
COPY ci/image-config-files/common/ /root/common/
COPY ci/image-config-files/redis/redis.conf /etc/redis/redis.conf
COPY ci/image-config-files/supervisor/supervisord.conf /etc/supervisor/supervisord.conf
COPY ci/image-config-files/fail2ban/ /etc/fail2ban/

# Copy entrypoint script
COPY ci/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Make fail2ban scripts executable
RUN chmod +x /root/common/fail2ban-status.sh /root/common/test-fail2ban.sh

EXPOSE 80

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]
