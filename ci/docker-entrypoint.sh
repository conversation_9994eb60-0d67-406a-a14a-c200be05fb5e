#!/bin/bash
set -e

# Set up base directories
mkdir -p /etc/nginx/modules-enabled \
         /root/common \
         /run/supervisor \
         /var/run/nginx \
         /etc/supervisor \
         /var/log/nginx \
         /var/log/redis \
         /var/log/api \
         /var/log/queue \
         /var/log/supervisor \
         /var/log/rsyslog \
         /var/log/fail2ban \
         /var/run/fail2ban \
         /tmp \
         /var/lib/nginx/tmp \
         /root/.npm \
         /root/.cache \
         /root/app/sites \
         /root/app/db

# Create and set permissions for supervisor pid
touch /run/supervisor/supervisord.pid

# Set directory permissions
chmod -R 777 /tmp \
             /var/lib/nginx/tmp \
             /root/.npm \
             /root/.cache \
             /root/app \
             /root/common \
             /var/log

# Set up supervisor log
touch /var/log/supervisor/supervisord.log
chmod 777 /var/log/supervisor/supervisord.log

# Set specific file permissions
chmod 640 /etc/redis/redis.conf
chmod 644 /run/supervisor/supervisord.pid

# Set up basic auth and nginx config
htpasswd -bc /etc/nginx/.htpasswd "$BASIC_AUTH_USER" "$BASIC_AUTH_PASS"
export BASIC_AUTH_ENABLED="\"Authentication Required\""

# Replace environment variables in nginx config
envsubst '${DOMAIN} ${BASIC_AUTH_ENABLED}' < /etc/nginx/conf.d/preview-sites.conf.template > /etc/nginx/conf.d/preview-sites.conf

# Set up fail2ban
echo "Setting up fail2ban..."

# Create fail2ban socket directory
mkdir -p /var/run/fail2ban

# Set permissions for fail2ban
chmod 755 /var/run/fail2ban
chmod 644 /etc/fail2ban/jail.local
chmod 644 /etc/fail2ban/filter.d/*.conf

# Create fail2ban log file
touch /var/log/fail2ban.log
chmod 644 /var/log/fail2ban.log

# Initialize iptables (required for fail2ban)
# Create basic iptables rules if they don't exist
if ! iptables -L INPUT >/dev/null 2>&1; then
    echo "Initializing iptables..."
    iptables -P INPUT ACCEPT
    iptables -P FORWARD ACCEPT
    iptables -P OUTPUT ACCEPT
    iptables -F
fi

echo "fail2ban setup complete"

# Execute the main command
exec "$@"
