#!/bin/bash
set -e

# Set up base directories
mkdir -p /etc/nginx/modules-enabled \
         /root/common \
         /run/supervisor \
         /var/run/nginx \
         /etc/supervisor \
         /var/log/nginx \
         /var/log/redis \
         /var/log/api \
         /var/log/queue \
         /var/log/supervisor \
         /tmp \
         /var/lib/nginx/tmp \
         /root/.npm \
         /root/.cache \
         /root/app/sites \
         /root/app/db

# Create and set permissions for supervisor pid
touch /run/supervisor/supervisord.pid

# Set directory permissions
chmod -R 777 /tmp \
             /var/lib/nginx/tmp \
             /root/.npm \
             /root/.cache \
             /root/app \
             /root/common \
             /var/log

# Set up supervisor log
touch /var/log/supervisor/supervisord.log
chmod 777 /var/log/supervisor/supervisord.log

# Set specific file permissions
chmod 640 /etc/redis/redis.conf
chmod 644 /run/supervisor/supervisord.pid

# Set up basic auth and nginx config
htpasswd -bc /etc/nginx/.htpasswd "$BASIC_AUTH_USER" "$BASIC_AUTH_PASS"
export BASIC_AUTH_ENABLED="\"Authentication Required\""

# Replace environment variables in nginx config
envsubst '${DOMAIN} ${BASIC_AUTH_ENABLED}' < /etc/nginx/conf.d/preview-sites.conf.template > /etc/nginx/conf.d/preview-sites.conf

# Execute the main command
exec "$@"
