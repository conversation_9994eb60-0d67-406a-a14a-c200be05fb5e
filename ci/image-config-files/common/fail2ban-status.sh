#!/bin/bash

# Fail2ban Status Monitoring Script
# This script provides comprehensive status information about fail2ban

echo "=== Fail2ban Status Report ==="
echo "Generated at: $(date)"
echo

# Check if fail2ban is running
if pgrep -f fail2ban-server > /dev/null; then
    echo "✅ Fail2ban is running"
    
    # Get fail2ban status
    echo
    echo "=== Fail2ban Server Status ==="
    fail2ban-client status 2>/dev/null || echo "❌ Could not get fail2ban status"
    
    echo
    echo "=== Active Jails ==="
    JAILS=$(fail2ban-client status 2>/dev/null | grep "Jail list:" | sed 's/.*Jail list:\s*//' | tr ',' '\n' | tr -d ' ')
    
    if [ -n "$JAILS" ]; then
        for jail in $JAILS; do
            echo
            echo "--- Jail: $jail ---"
            fail2ban-client status "$jail" 2>/dev/null || echo "❌ Could not get status for jail $jail"
        done
    else
        echo "No active jails found"
    fi
    
    echo
    echo "=== Recent Ban Activity ==="
    if [ -f /var/log/fail2ban-actions.log ]; then
        echo "Last 10 ban/unban actions:"
        tail -10 /var/log/fail2ban-actions.log 2>/dev/null || echo "No recent actions found"
    else
        echo "No action log file found"
    fi
    
    echo
    echo "=== Current iptables Rules ==="
    echo "Fail2ban chains:"
    iptables -L | grep -E "^Chain f2b-" || echo "No fail2ban chains found"
    
    echo
    echo "=== Log File Status ==="
    echo "Fail2ban log size: $(du -h /var/log/fail2ban.log 2>/dev/null | cut -f1 || echo 'N/A')"
    echo "Nginx access log size: $(du -h /var/log/nginx/access.log 2>/dev/null | cut -f1 || echo 'N/A')"
    echo "Nginx error log size: $(du -h /var/log/nginx/error.log 2>/dev/null | cut -f1 || echo 'N/A')"
    
else
    echo "❌ Fail2ban is not running"
    
    echo
    echo "=== Checking for fail2ban process ==="
    ps aux | grep fail2ban | grep -v grep || echo "No fail2ban processes found"
    
    echo
    echo "=== Checking fail2ban logs ==="
    if [ -f /var/log/fail2ban.log ]; then
        echo "Last 5 lines from fail2ban.log:"
        tail -5 /var/log/fail2ban.log
    else
        echo "No fail2ban.log found"
    fi
fi

echo
echo "=== System Information ==="
echo "Uptime: $(uptime)"
echo "Memory usage: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "Disk usage: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"

echo
echo "=== End of Report ==="
