#!/bin/bash

# Test script for fail2ban configuration
# This script tests the fail2ban setup and configuration

echo "=== Fail2ban Configuration Test ==="
echo "Testing fail2ban setup and configuration..."
echo

# Test 1: Check if fail2ban is installed
echo "1. Checking fail2ban installation..."
if command -v fail2ban-client >/dev/null 2>&1; then
    echo "✅ fail2ban-client is installed"
else
    echo "❌ fail2ban-client is not installed"
    exit 1
fi

# Test 2: Check configuration files
echo
echo "2. Checking configuration files..."

CONFIG_FILES=(
    "/etc/fail2ban/jail.local"
    "/etc/fail2ban/filter.d/flash-preview-api.conf"
    "/etc/fail2ban/filter.d/flash-preview-auth.conf"
    "/etc/fail2ban/filter.d/nginx-badbots.conf"
    "/etc/fail2ban/action.d/iptables-multiport-log.conf"
)

for file in "${CONFIG_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file is missing"
    fi
done

# Test 3: Check if fail2ban can start (dry run)
echo
echo "3. Testing fail2ban configuration syntax..."
if fail2ban-client -t >/dev/null 2>&1; then
    echo "✅ fail2ban configuration syntax is valid"
else
    echo "❌ fail2ban configuration has syntax errors"
    echo "Running fail2ban-client -t for details:"
    fail2ban-client -t
fi

# Test 4: Check log files and directories
echo
echo "4. Checking log files and directories..."

LOG_DIRS=(
    "/var/log"
    "/var/run/fail2ban"
)

for dir in "${LOG_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ Directory $dir exists"
    else
        echo "❌ Directory $dir is missing"
    fi
done

# Test 5: Check iptables availability
echo
echo "5. Checking iptables availability..."
if command -v iptables >/dev/null 2>&1; then
    echo "✅ iptables is available"
    
    # Try to list iptables rules (this might fail in some container environments)
    if iptables -L >/dev/null 2>&1; then
        echo "✅ iptables is functional"
    else
        echo "⚠️  iptables may not be fully functional (this is normal in some container environments)"
    fi
else
    echo "❌ iptables is not available"
fi

# Test 6: Check nginx log format
echo
echo "6. Checking nginx log configuration..."
if [ -f "/etc/nginx/nginx.conf" ]; then
    if grep -q "log_format fail2ban" /etc/nginx/nginx.conf; then
        echo "✅ nginx fail2ban log format is configured"
    else
        echo "❌ nginx fail2ban log format is missing"
    fi
    
    if grep -q "access_log.*fail2ban" /etc/nginx/nginx.conf; then
        echo "✅ nginx access log with fail2ban format is configured"
    else
        echo "❌ nginx access log with fail2ban format is missing"
    fi
else
    echo "❌ nginx.conf not found"
fi

echo
echo "=== Test Summary ==="
echo "If all tests show ✅, fail2ban should work correctly when the container starts."
echo "Some ⚠️  warnings are normal in container environments."
echo "Any ❌ errors should be investigated and fixed."
echo
echo "To start fail2ban manually for testing:"
echo "  fail2ban-server -f"
echo
echo "To check status after starting:"
echo "  fail2ban-client status"
