# Fail2Ban action configuration for iptables with logging
# This action blocks IPs using iptables and logs the action

[Definition]

# Option: actionstart
# Notes.: command executed once at the start of Fail2Ban.
# Values: CMD
actionstart = iptables -N f2b-<name>
              iptables -A f2b-<name> -j RETURN
              iptables -I <chain> -p <protocol> -m multiport --dports <port> -j f2b-<name>

# Option: actionstop
# Notes.: command executed once at the end of Fail2Ban
# Values: CMD
actionstop = iptables -D <chain> -p <protocol> -m multiport --dports <port> -j f2b-<name>
             iptables -F f2b-<name>
             iptables -X f2b-<name>

# Option: actioncheck
# Notes.: command executed once before each actionban command
# Values: CMD
actioncheck = iptables -n -L <chain> | grep -q 'f2b-<name>[ \t]'

# Option: actionban
# Notes.: command executed when banning an IP. Take care that the
#         command is executed with Fail2Ban user rights.
# Tags:   See jail.conf(5) man page
# Values: CMD
actionban = iptables -I f2b-<name> 1 -s <ip> -j <blocktype>
            echo "$(date): Banned IP <ip> for jail <name>" >> /var/log/fail2ban-actions.log

# Option: actionunban
# Notes.: command executed when unbanning an IP. Take care that the
#         command is executed with Fail2Ban user rights.
# Tags:   See jail.conf(5) man page
# Values: CMD
actionunban = iptables -D f2b-<name> -s <ip> -j <blocktype>
              echo "$(date): Unbanned IP <ip> for jail <name>" >> /var/log/fail2ban-actions.log

[Init]

# Default name of the chain
name = default

# Option: port
# Notes.: specifies port to monitor
# Values: [ NUM | STRING ]  Default:
port = ssh

# Option: protocol
# Notes.: internally used by config reader for interpolations.
# Values: [ tcp | udp | icmp | all ] Default: tcp
protocol = tcp

# Option: chain
# Notes.: specifies the iptables chain to which the fail2ban rules should be
#         added
# Values: STRING  Default: INPUT
chain = INPUT

# Option: blocktype
# Notes.: This is what the action does with rules. This can be any jump target
#         as per the iptables man page (section 8). Common values are DROP
#         REJECT, REJECT --reject-with icmp-port-unreachable
# Values: STRING
blocktype = REJECT --reject-with icmp-port-unreachable
