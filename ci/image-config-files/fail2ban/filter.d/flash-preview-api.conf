# Fail2Ban filter for Flash Preview Server API abuse
# This filter detects excessive API calls to the flash preview server

[Definition]

# Detect too many API calls from the same IP
failregex = ^<HOST> - .* "(POST|GET) /(deploy|update|jobs|queues)/.* HTTP/.*" (4\d\d|5\d\d) .*$
            ^<HOST> - .* "(POST|GET) /(deploy|update)/.* HTTP/.*" 200 .*$

# Ignore successful health checks and normal operations
ignoreregex = ^<HOST> - .* "GET /health HTTP/.*" 200 .*$
              ^<HOST> - .* "GET / HTTP/.*" 200 .*$
              ^<HOST> - .* "OPTIONS .* HTTP/.*" 200 .*$

# Date pattern for nginx logs
datepattern = %%d/%%b/%%Y:%%H:%%M:%%S %%z
