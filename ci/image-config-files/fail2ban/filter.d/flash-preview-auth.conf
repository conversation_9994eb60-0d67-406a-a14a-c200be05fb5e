# Fail2Ban filter for Flash Preview Server authentication failures
# This filter detects failed basic authentication attempts

[Definition]

# Detect 401 Unauthorized responses (failed basic auth)
failregex = ^<HOST> - .* "(GET|POST|PUT|DELETE) .* HTTP/.*" 401 .*$

# Ignore legitimate OPTIONS requests
ignoreregex = ^<HOST> - .* "OPTIONS .* HTTP/.*" 401 .*$

# Date pattern for nginx logs
datepattern = %%d/%%b/%%Y:%%H:%%M:%%S %%z
