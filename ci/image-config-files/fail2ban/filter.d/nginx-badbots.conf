# Fail2Ban filter for bad bots and scanners
# This filter detects common bot patterns and scanning attempts

[Definition]

# Detect bad bots, scanners, and malicious user agents
failregex = ^<HOST> -.*"(GET|POST).*HTTP.*".*".*(?:bot|crawler|spider|scan|hack|exploit|injection|sql|xss|script|shell|cmd|eval|exec|system|proc|file|directory|path|traversal|include|require|upload|download|backdoor|trojan|virus|malware|worm|rootkit|keylogger|spyware|adware|ransomware|phishing|fraud|scam|spam|abuse|attack|intrusion|penetration|vulnerability|security|test|probe|enum|brute|force|crack|bypass|overflow|injection|payload|shellcode|reverse|bind|connect|tunnel|proxy|redirect|forward|bounce|relay|gateway|bridge|hop|jump|pivot|lateral|escalation|privilege|elevation|admin|root|sudo|su|chmod|chown|passwd|shadow|hosts|resolv|crontab|bashrc|profile|history|log|tmp|var|etc|usr|bin|sbin|lib|opt|srv|home|root|boot|dev|proc|sys|mnt|media|run|snap).*"$
            ^<HOST> -.*"(GET|POST).*(\.php|\.asp|\.aspx|\.jsp|\.cgi|\.pl|\.py|\.rb|\.sh|\.bat|\.cmd|\.exe|\.dll|\.so|\.jar|\.war|\.ear|\.zip|\.rar|\.tar|\.gz|\.bz2|\.7z|\.sql|\.db|\.mdb|\.bak|\.old|\.tmp|\.log|\.conf|\.cfg|\.ini|\.xml|\.json|\.yml|\.yaml|\.env|\.git|\.svn|\.hg|\.bzr|\.cvs).*HTTP.*".*$
            ^<HOST> -.*"(GET|POST).*(admin|administrator|wp-admin|wp-login|phpmyadmin|cpanel|webmail|roundcube|squirrelmail|horde|imp|turba|kronolith|nag|mnemo|gollem|passwd|chpasswd|htpasswd|\.htaccess|\.htpasswd|web\.config|robots\.txt|sitemap\.xml|crossdomain\.xml|clientaccesspolicy\.xml).*HTTP.*".*$

# Ignore legitimate requests
ignoreregex = ^<HOST> -.*"(GET|POST).*(\.html|\.htm|\.css|\.js|\.png|\.jpg|\.jpeg|\.gif|\.svg|\.ico|\.woff|\.woff2|\.ttf|\.eot|\.otf|\.pdf|\.doc|\.docx|\.xls|\.xlsx|\.ppt|\.pptx|\.txt|\.csv|\.json|\.xml|\.rss|\.atom).*HTTP.*" (200|304) .*$

# Date pattern for nginx logs
datepattern = %%d/%%b/%%Y:%%H:%%M:%%S %%z
