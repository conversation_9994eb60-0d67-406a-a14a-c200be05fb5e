[DEFAULT]
# Ban hosts for 1 hour (3600 seconds)
bantime = 3600

# A host is banned if it has generated "maxretry" during the last "findtime" seconds
findtime = 600

# Number of failures before a host gets banned
maxretry = 5

# Destination email address used solely for the interpolations in jail.{conf,local} configuration files
destemail = root@localhost

# Sender email address used solely for some actions
sender = root@localhost

# Default protocol
protocol = tcp

# Specify chain where jumps would need to be added in iptables-* actions
chain = INPUT

# Default action: ban with logging
action = iptables-multiport-log

# Enable logging
logtarget = /var/log/fail2ban.log

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/access.log
maxretry = 3
bantime = 1800

[nginx-noscript]
enabled = true
port = http,https
filter = nginx-noscript
logpath = /var/log/nginx/access.log
maxretry = 6
bantime = 600

[nginx-badbots]
enabled = true
port = http,https
filter = nginx-badbots
logpath = /var/log/nginx/access.log
maxretry = 2
bantime = 3600

[nginx-noproxy]
enabled = true
port = http,https
filter = nginx-noproxy
logpath = /var/log/nginx/access.log
maxretry = 2
bantime = 3600

[nginx-limit-req]
enabled = true
port = http,https
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
bantime = 600

[flash-preview-api]
enabled = true
port = http,https
filter = flash-preview-api
logpath = /var/log/nginx/access.log
maxretry = 10
bantime = 1800
findtime = 300

[flash-preview-auth]
enabled = true
port = http,https
filter = flash-preview-auth
logpath = /var/log/nginx/access.log
maxretry = 5
bantime = 3600
findtime = 600
