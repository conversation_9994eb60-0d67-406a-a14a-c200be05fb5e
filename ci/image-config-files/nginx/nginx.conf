user root;
worker_processes auto;
pid /var/lib/nginx/nginx.pid;

env DOMAIN;

events {
    worker_connections 1024;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Security: Hide Nginx version
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Custom log format without user agent and with full URL
    log_format custom '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" URL: $scheme://$host$request_uri';

    # Enhanced log format for fail2ban with more details
    log_format fail2ban '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent"';

    # Configure access logging with custom format
    access_log /dev/stdout custom;
    access_log /var/log/nginx/access.log fail2ban;
    error_log /dev/stderr warn;
    error_log /var/log/nginx/error.log warn;


    # Enable response time metrics
    proxy_request_buffering off;
    proxy_buffering off;

    gzip on;
    gzip_disable "msie6";

    # Performance: Enable gzip for more MIME types
    gzip_types
      application/atom+xml
      application/geo+json
      application/javascript
      application/x-javascript
      application/json
      application/ld+json
      application/manifest+json
      application/rdf+xml
      application/rss+xml
      application/vnd.ms-fontobject
      application/wasm
      application/x-web-app-manifest+json
      application/xhtml+xml
      application/xml
      font/eot
      font/otf
      font/ttf
      image/bmp
      image/svg+xml
      text/cache-manifest
      text/calendar
      text/css
      text/javascript
      text/markdown
      text/plain
      text/xml
      text/vcard
      text/vnd.rim.location.xloc
      text/vtt
      text/x-component
      text/x-cross-domain-policy;

    include /etc/nginx/conf.d/*.conf;
}
