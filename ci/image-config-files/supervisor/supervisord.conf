[supervisord]
user=root
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/run/supervisor/supervisord.pid
childlogdir=/var/log/supervisor
loglevel=debug

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;" -p /var/lib/nginx
autostart=true
autorestart=true
stdout_logfile=/var/log/nginx/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stdout_events_enabled=true
stdout_syslog=true
stderr_logfile=/var/log/nginx/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
stderr_events_enabled=true
stdout_dup_file=/dev/stdout
stderr_dup_file=/dev/stderr
priority=10
startretries=1
startsecs=2
stopwaitsecs=1

[program:redis]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
stdout_logfile=/var/log/redis/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stdout_events_enabled=true
stderr_logfile=/var/log/redis/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
stderr_events_enabled=true
stdout_dup_file=/dev/stdout
stderr_dup_file=/dev/stderr
priority=10
startretries=1
startsecs=2
stopwaitsecs=1

[program:api]
command=node --max-old-space-size=3072 dist/index.js
environment=NPM_CONFIG_UNSAFE_PERM=true
autostart=true
autorestart=true
stdout_logfile=/var/log/api/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stdout_events_enabled=true
stderr_logfile=/var/log/api/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
stderr_events_enabled=true
stdout_dup_file=/dev/stdout
stderr_dup_file=/dev/stderr
priority=20
startretries=1
startsecs=2
stopwaitsecs=1

[program:queue-worker]
command=node --max-old-space-size=3072 dist/queues/worker.js
environment=NPM_CONFIG_UNSAFE_PERM=true
autostart=true
autorestart=true
stdout_logfile=/var/log/queue/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stdout_events_enabled=true
stderr_logfile=/var/log/queue/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
stderr_events_enabled=true
stdout_dup_file=/dev/stdout
stderr_dup_file=/dev/stderr
priority=20
startretries=1
startsecs=2
stopwaitsecs=1

[program:rsyslog]
command=/usr/sbin/rsyslogd -n
autostart=true
autorestart=true
stdout_logfile=/var/log/rsyslog/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stderr_logfile=/var/log/rsyslog/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
priority=5
startretries=1
startsecs=2
stopwaitsecs=1

[program:fail2ban]
command=/usr/bin/fail2ban-server -f -s /var/run/fail2ban/fail2ban.sock -p /var/run/fail2ban/fail2ban.pid
autostart=true
autorestart=true
stdout_logfile=/var/log/fail2ban/out.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
stderr_logfile=/var/log/fail2ban/error.log
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=5
priority=15
startretries=3
startsecs=5
stopwaitsecs=10
