import type { FastifyInstance } from 'fastify'
import fs from 'fs/promises'
import { nanoid } from 'nanoid'

import { getWorkDir } from '../config.js'
import { createJob, getJob, getJobs, getRepoJobs } from '../db.js'
import { validateRepository } from '../middleware/validateRepository.js'
import { queues } from '../queues/index.js'
import {
  DeployRequest,
  JobResponse,
  JobsListResponse,
  TriggerDetails,
  UpdateRequest,
  UpdateStoryRequest
} from '../types.js'

async function checkWorkingDirectory(repo: string): Promise<boolean> {
  try {
    await fs.access(getWorkDir(repo))
    return true
  } catch {
    return false
  }
}

function extractTriggerDetails(request: {
  headers: Record<string, string | string[] | undefined>
  body: unknown
}): TriggerDetails {
  return {
    body: request.body,
    headers: request.headers
  }
}

export async function jobRoutes(fastify: FastifyInstance) {
  // Error handler
  fastify.setErrorHandler((error, _request, reply) => {
    fastify.log.error(error)
    void reply.status(500).send({
      error: 'Internal Server Error',
      message: error.message
    })
  })

  const routeOptions = {
    preValidation: validateRepository
  }

  fastify.post<DeployRequest>('/deploy/:repo', routeOptions, async (request, _reply): Promise<JobResponse> => {
    const { repo } = request.params
    const jobId = nanoid()
    const triggerDetails = extractTriggerDetails(request)

    const job = createJob({
      jobId,
      repo,
      triggerDetails,
      type: 'DEPLOY'
    })

    await queues.sequential.add('DEPLOY ' + repo, job, { jobId })

    return { job }
  })

  fastify.post<UpdateRequest>('/update/:repo', routeOptions, async (request): Promise<JobResponse> => {
    const { repo } = request.params
    const exists = await checkWorkingDirectory(repo)

    const jobId = nanoid()
    const triggerDetails = extractTriggerDetails(request)

    const job = createJob({
      jobId,
      repo,
      triggerDetails,
      type: exists ? 'UPDATE' : 'DEPLOY'
    })

    await queues.sequential.add('UPDATE ' + repo, job)

    return { job }
  })

  fastify.post<UpdateStoryRequest>(
    '/update/:repo/story/:storyId',
    routeOptions,
    async (request): Promise<JobResponse> => {
      const { repo, storyId } = request.params
      const exists = await checkWorkingDirectory(repo)

      const jobId = nanoid()
      const triggerDetails = extractTriggerDetails(request)

      const job = createJob({
        jobId,
        repo,
        storyId,
        triggerDetails,
        type: exists ? 'UPDATE_ONE' : 'DEPLOY'
      })

      await queues.concurrent.add('UPDATE_ONE ' + repo, job)

      return { job }
    }
  )

  fastify.get<{ Params: { repo: string } }>(
    '/repo/:repo/jobs',
    routeOptions,
    async (request): Promise<JobsListResponse> => {
      const { repo } = request.params
      return { jobs: getRepoJobs(repo) }
    }
  )

  fastify.get<{ Params: { id: string } }>('/jobs/:id', async (request, reply): Promise<JobResponse> => {
    const { id } = request.params
    const job = getJob(id)

    if (!job) {
      void reply.status(404).send({
        error: 'Not Found',
        message: 'Job not found'
      })
      return reply
    }

    return { job }
  })

  fastify.get('/jobs', async (): Promise<JobsListResponse> => {
    return { jobs: getJobs() }
  })
}
