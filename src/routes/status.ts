import os from 'node:os'

import { FastifyInstance } from 'fastify'
import { Redis } from 'ioredis'

import { db } from '../db.js'
import { queues } from '../queues/index.js'

const redis = new Redis({ host: 'localhost', port: 6379 })
const startTime = Date.now()

const checkDbStatus = () => {
  try {
    db.prepare('SELECT 1').get()
    return true
  } catch {
    return false
  }
}

const checkRedisStatus = async () => {
  try {
    await redis.ping()
    return true
  } catch {
    return false
  }
}

const checkQueuesStatus = async () => {
  try {
    const [concurrent, sequential] = await Promise.all([
      queues.concurrent.getJobCounts(),
      queues.sequential.getJobCounts()
    ])

    const queuesStatus = { concurrent, sequential }

    return { details: queuesStatus, ok: true }
  } catch (error) {
    return { details: error, ok: false }
  }
}

export const statusRoutes = async (server: FastifyInstance) => {
  server.get('/', async (request) => {
    const [redisOk, queuesStatus] = await Promise.all([checkRedisStatus(), checkQueuesStatus()])
    const baseUrl = `${request.protocol}://${request.hostname}`
    const getNonSensitiveEnvVars = () => {
      const sensitiveKeys = ['GH_PRIVATE_KEY', 'BASIC_AUTH_PASS', 'GH_APP_ID', 'GH_APP_INSTALLATION_ID']
      return Object.entries(process.env)
        .filter(([key]) => !sensitiveKeys.includes(key))
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
    }

    const lastCommit = process.env.LAST_COMMIT_MESSAGE || 'Not available'

    return {
      components: {
        database: {
          status: checkDbStatus() ? 'ok' : 'error',
          type: 'sqlite'
        },
        queues: {
          details: queuesStatus.details,
          status: queuesStatus.ok ? 'ok' : 'error'
        },
        redis: {
          status: redisOk ? 'ok' : 'error'
        }
      },
      endpoints: {
        healthCheck: `GET ${baseUrl}/health`,
        jobs: {
          all: `GET ${baseUrl}/jobs`,
          byRepo: `GET ${baseUrl}/jobs/{repo}`
        },
        logs: `GET ${baseUrl}/logs`,
        operations: {
          deploy: `POST ${baseUrl}/deploy/{repo}`,
          update: {
            repository: `POST ${baseUrl}/update/{repo}`,
            story: `POST ${baseUrl}/update/{repo}/story/{storyId}`
          }
        },
        queues: `GET ${baseUrl}/queues/`
      },
      environment: getNonSensitiveEnvVars(),
      lastCommit,
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: {
        server: {
          started: new Date(startTime).toISOString(),
          uptime: Date.now() - startTime
        },
        system: {
          platform: os.platform(),
          release: os.release(),
          uptime: os.uptime() * 1000,
          cpus: os.cpus().length
        }
      }
    }
  })
}
