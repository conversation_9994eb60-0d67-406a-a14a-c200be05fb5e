import { exec } from 'node:child_process'
import os from 'node:os'
import { promisify } from 'node:util'

import { FastifyInstance } from 'fastify'
import { Redis } from 'ioredis'

import { db } from '../db.js'
import { queues } from '../queues/index.js'

const execAsync = promisify(exec)

const redis = new Redis({ host: 'localhost', port: 6379 })
const startTime = Date.now()

const checkDbStatus = () => {
  try {
    db.prepare('SELECT 1').get()
    return true
  } catch {
    return false
  }
}

const checkRedisStatus = async () => {
  try {
    await redis.ping()
    return true
  } catch {
    return false
  }
}

const checkQueuesStatus = async () => {
  try {
    const [concurrent, sequential] = await Promise.all([
      queues.concurrent.getJobCounts(),
      queues.sequential.getJobCounts()
    ])

    const queuesStatus = { concurrent, sequential }

    return { details: queuesStatus, ok: true }
  } catch (error) {
    return { details: error, ok: false }
  }
}

const checkFail2banStatus = async () => {
  try {
    // Check if fail2ban is running
    const { stdout: statusOutput } = await execAsync(
      'fail2ban-client status 2>/dev/null || echo "fail2ban not running"'
    )

    if (statusOutput.includes('fail2ban not running')) {
      return {
        status: 'error',
        details: { message: 'fail2ban is not running' },
        ok: false
      }
    }

    // Get jail status
    const jailsMatch = statusOutput.match(/Jail list:\s*(.+)/)
    const jails = jailsMatch
      ? jailsMatch[1]
          .split(',')
          .map((j) => j.trim())
          .filter((j) => j)
      : []

    const jailDetails: Record<string, any> = {}
    for (const jail of jails) {
      try {
        const { stdout: jailStatus } = await execAsync(`fail2ban-client status "${jail}" 2>/dev/null`)
        const currentlyBanned = jailStatus.match(/Currently banned:\s*(\d+)/)
        const totalBanned = jailStatus.match(/Total banned:\s*(\d+)/)

        jailDetails[jail] = {
          currentlyBanned: currentlyBanned ? parseInt(currentlyBanned[1], 10) : 0,
          totalBanned: totalBanned ? parseInt(totalBanned[1], 10) : 0
        }
      } catch {
        jailDetails[jail] = { error: 'Could not get jail status' }
      }
    }

    return {
      status: 'ok',
      details: {
        jails: jailDetails,
        activeJails: jails.length
      },
      ok: true
    }
  } catch (error) {
    return {
      status: 'error',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      ok: false
    }
  }
}

export const statusRoutes = async (server: FastifyInstance) => {
  server.get('/', async (request) => {
    const [redisOk, queuesStatus, fail2banStatus] = await Promise.all([
      checkRedisStatus(),
      checkQueuesStatus(),
      checkFail2banStatus()
    ])
    const baseUrl = `${request.protocol}://${request.hostname}`
    const getNonSensitiveEnvVars = () => {
      const sensitiveKeys = ['GH_PRIVATE_KEY', 'BASIC_AUTH_PASS', 'GH_APP_ID', 'GH_APP_INSTALLATION_ID']
      return Object.entries(process.env)
        .filter(([key]) => !sensitiveKeys.includes(key))
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
    }

    const lastCommit = process.env.LAST_COMMIT_MESSAGE || 'Not available'

    return {
      components: {
        database: {
          status: checkDbStatus() ? 'ok' : 'error',
          type: 'sqlite'
        },
        queues: {
          details: queuesStatus.details,
          status: queuesStatus.ok ? 'ok' : 'error'
        },
        redis: {
          status: redisOk ? 'ok' : 'error'
        },
        fail2ban: {
          status: fail2banStatus.status,
          details: fail2banStatus.details
        }
      },
      endpoints: {
        healthCheck: `GET ${baseUrl}/health`,
        jobs: {
          all: `GET ${baseUrl}/jobs`,
          byRepo: `GET ${baseUrl}/jobs/{repo}`
        },
        logs: `GET ${baseUrl}/logs`,
        operations: {
          deploy: `POST ${baseUrl}/deploy/{repo}`,
          update: {
            repository: `POST ${baseUrl}/update/{repo}`,
            story: `POST ${baseUrl}/update/{repo}/story/{storyId}`
          }
        },
        queues: `GET ${baseUrl}/queues/`,
        security: {
          fail2ban: `GET ${baseUrl}/fail2ban`
        }
      },
      environment: getNonSensitiveEnvVars(),
      lastCommit,
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: {
        server: {
          started: new Date(startTime).toISOString(),
          uptime: Date.now() - startTime
        },
        system: {
          platform: os.platform(),
          release: os.release(),
          uptime: os.uptime() * 1000,
          cpus: os.cpus().length
        }
      }
    }
  })

  // Dedicated fail2ban status endpoint
  server.get('/fail2ban', async () => {
    const fail2banStatus = await checkFail2banStatus()

    // Get additional fail2ban information
    try {
      const { stdout: logTail } = await execAsync(
        'tail -20 /var/log/fail2ban.log 2>/dev/null || echo "No log available"'
      )
      const { stdout: actionLog } = await execAsync(
        'tail -10 /var/log/fail2ban-actions.log 2>/dev/null || echo "No action log available"'
      )

      return {
        ...fail2banStatus,
        logs: {
          recent: logTail.split('\n').filter((line) => line.trim()),
          actions: actionLog.split('\n').filter((line) => line.trim())
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        ...fail2banStatus,
        logs: { error: 'Could not retrieve logs' },
        timestamp: new Date().toISOString()
      }
    }
  })
}
